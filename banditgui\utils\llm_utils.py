import os
from typing import Any, Dict, List, Union

from banditgui.config.logging import get_logger

logger = get_logger('utils.llm_utils')

def is_api_key_valid(provider: str) -> bool:
    """
    Checks if the API key for a given LLM provider is set and valid (not a placeholder).

    Args:
        provider (str): The name of the LLM provider (e.g., "openai", "groq").

    Returns:
        bool: True if the API key is valid or if the provider is ollama (no key needed),
              False otherwise.
    """
    if provider == "ollama":
        return True
    api_key_env_var = f"{provider.upper()}_API_KEY"
    api_key = os.getenv(api_key_env_var)
    return bool(api_key and api_key != f"YOUR_{provider.upper()}_API_KEY_HERE")

def get_model_ids_from_config(models: Union[Dict[str, Any], List[str]], provider: str) -> List[str]:
    """
    Extracts model IDs from the given model configuration (dict or list).

    Args:
        models (Union[Dict, List]): The model configuration from valid_model_list.json.
        provider (str): The name of the LLM provider.

    Returns:
        List[str]: A list of model IDs.

    Raises:
        ValidationError: If models format is unexpected.
    """
    if isinstance(models, dict):
        return list(models.keys())
    elif isinstance(models, list):
        return models
    else:
        logger.warning(f"Unexpected model format for provider {provider}. Expected dict or list.")
        return []

def construct_ask_a_pro_prompt(level_name: str, level_description: str, command_history_list: List[str]) -> str:
    """
    Construct a prompt for Ask-a-Pro.

    Args:
        level_name: The name of the level
        level_description: The description of the level
        command_history_list: List of commands executed by the user

    Returns:
        str: Constructed prompt
    """
    command_history_str = "\n".join([f"- {cmd}" for cmd in command_history_list])

    prompt_template = f"""
You are an expert Ask-a-Pro assisting a CTF or security challenge participant. The user is currently on a specific level, and has executed the following commands so far.

Goal: Help them understand their current situation. Provide some technical explanation, suggest a direction or next step, and recommend a resource to learn more. Never reveal the full solution. Keep it constructive and educational.

---

📍 Level Name: {level_name}
🧩 Level Description: {level_description}

---

Executed Commands:
{command_history_str}
"""

    return prompt_template 