import os
from typing import Any, Dict, List, Union

import requests

from banditgui.config.logging import get_logger

logger = get_logger('utils.llm_utils')

def is_api_key_valid(provider: str) -> bool:
    """
    Checks if the API key for a given LLM provider is set and valid (not a placeholder).

    Args:
        provider (str): The name of the LLM provider (e.g., "openai", "groq").

    Returns:
        bool: True if the API key is valid or if the provider is ollama (no key needed),
              False otherwise.
    """
    if provider == "ollama":
        return True
    api_key_env_var = f"{provider.upper()}_API_KEY"
    api_key = os.getenv(api_key_env_var)
    return bool(api_key and api_key != f"YOUR_{provider.upper()}_API_KEY_HERE")

def get_model_ids_from_config(models: Union[Dict[str, Any], List[str]], provider: str) -> List[str]:
    """
    Extracts model IDs from the given model configuration (dict or list).

    Args:
        models (Union[Dict, List]): The model configuration from valid_model_list.json.
        provider (str): The name of the LLM provider.

    Returns:
        List[str]: A list of model IDs.

    Raises:
        ValidationError: If models format is unexpected.
    """
    if isinstance(models, dict):
        return list(models.keys())
    elif isinstance(models, list):
        return models
    else:
        logger.warning(f"Unexpected model format for provider {provider}. Expected dict or list.")
        return []

def construct_ask_a_pro_prompt(level_name: str, level_description: str, command_history_list: List[str]) -> str:
    """
    Construct a prompt for Ask-a-Pro.

    Args:
        level_name: The name of the level
        level_description: The description of the level
        command_history_list: List of commands executed by the user

    Returns:
        str: Constructed prompt
    """
    command_history_str = "\n".join([f"- {cmd}" for cmd in command_history_list])

    prompt_template = f"""
You are an expert Ask-a-Pro assisting a CTF or security challenge participant. The user is currently on a specific level, and has executed the following commands so far.

Goal: Help them understand their current situation. Provide some technical explanation, suggest a direction or next step, and recommend a resource to learn more. Never reveal the full solution. Keep it constructive and educational.

---

📍 Level Name: {level_name}
🧩 Level Description: {level_description}

---

Executed Commands:
{command_history_str}
"""

    return prompt_template


def generate_dynamic_model_list() -> Dict[str, List[str]]:
    """
    Generate a dynamic model list based on available API keys.

    Returns:
        Dict[str, List[str]]: Dictionary with provider names as keys and model lists as values
    """

    model_list = {}

    # Check which API keys are available (not placeholder values)
    available_providers = []
    api_key_mapping = {
        'openai': 'OPENAI_API_KEY',
        'groq': 'GROQ_API_KEY',
        'gemini': 'GEMINI_API_KEY',
        'github': 'GITHUB_API_KEY',
        'openrouter': 'OPENROUTER_API_KEY'
    }

    for provider, env_var in api_key_mapping.items():
        api_key = os.getenv(env_var)
        if api_key and not api_key.startswith('YOUR_') and api_key != f"YOUR_{env_var}_HERE":
            available_providers.append(provider)
            logger.info(f"Found valid API key for {provider}")

    # Always add empty entries for OpenAI, Groq, and GitHub
    model_list['openai'] = []
    model_list['groq'] = []
    model_list['github'] = []

    # Fetch and filter Gemini models if API key is available
    if 'gemini' in available_providers:
        try:
            import google.generativeai as genai
            genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

            gemini_models = [m.name for m in genai.list_models()]
            # Filter for models containing "flash", "pro", or "preview"
            filtered_gemini = [
                m for m in gemini_models
                if any(keyword in m.lower() for keyword in ['flash', 'pro', 'preview'])
            ]
            model_list['gemini'] = filtered_gemini
            logger.info(f"Fetched {len(filtered_gemini)} Gemini models")

        except Exception as e:
            logger.error(f"Error fetching Gemini models: {e}")
            model_list['gemini'] = []
    else:
        model_list['gemini'] = []

    # Fetch and filter OpenRouter models if API key is available
    if 'openrouter' in available_providers:
        try:
            response = requests.get("https://openrouter.ai/api/v1/models")
            response.raise_for_status()
            openrouter_data = response.json()

            # Filter for models with "free" in their name
            free_models = []
            for model in openrouter_data.get('data', []):
                if model.get('name') and 'free' in model['name'].lower():
                    free_models.append(model['id'])

            model_list['openrouter'] = free_models
            logger.info(f"Fetched {len(free_models)} free OpenRouter models")

        except Exception as e:
            logger.error(f"Error fetching OpenRouter models: {e}")
            model_list['openrouter'] = []
    else:
        model_list['openrouter'] = []

    logger.info(f"Generated model list with {len(model_list)} providers")
    return model_list