"""
Data-related routes for BanditGUI.
"""

import json
import os

from flask import Blueprint, current_app, jsonify, request

from banditgui.config.logging import get_logger
from banditgui.utils.decorators import api_error_handler
from banditgui.utils.level_info import get_level_info

# Initialize logger for this blueprint
data_bp_logger = get_logger('data.routes')

# Create a Blueprint
data_bp = Blueprint('data', __name__, url_prefix='/data')

# This will be set by the main app when the blueprint is registered
# No managers are directly needed for current level info based on current implementation, but can be added if needed.

@data_bp.route('/level-info', methods=['POST'])
@api_error_handler
def level_info():
    """Get information about a specific level."""
    level = request.json.get('level', 0)
    data_bp_logger.info(f"Requested level info for level {level}")

    level_data = get_level_info(level)

    if level_data:
        data_bp_logger.debug(f"Found level info for level {level}")
        return jsonify({'status': 'success', 'levelInfo': level_data})
    else:
        data_bp_logger.warning(f"Level info not found for level {level}")
        return jsonify({
            'status': 'error',
            'message': f'Level {level} information not found'
        }), 404 # Return 404 for not found


@data_bp.route('/llm-models', methods=['GET'])
@api_error_handler
def get_llm_models():
    """Get the list of available LLM models."""
    data_bp_logger.info("Requested LLM models list")
    try:
        # Construct the path to llm_model.json
        config_path = os.path.join(current_app.root_path, 'config', 'llm_model.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            llm_models = json.load(f)
        return jsonify(llm_models)
    except FileNotFoundError:
        data_bp_logger.error("llm_model.json not found.")
        return jsonify({"status": "error", "message": "LLM models configuration file not found."}), 500
    except json.JSONDecodeError:
        data_bp_logger.error("Error decoding llm_model.json.")
        return jsonify({"status": "error", "message": "Error reading LLM models configuration."}), 500
    except Exception as e:
        data_bp_logger.error(f"An unexpected error occurred: {e}")
        return jsonify({"status": "error", "message": "An unexpected error occurred while fetching LLM models."}), 500
